using System;
using System.Collections.Generic;
using System.Linq;
using CompanyCleanTitleV2;

namespace CompanyCleanTitleV2.Tests
{
    /// <summary>
    /// Comprehensive test suite for average salary extraction functionality
    /// Tests various patterns and edge cases for extracting explicit average salary mentions
    /// </summary>
    public class AvgSalaryTests
    {
        private readonly AvgSalary _avgSalary;

        public AvgSalaryTests()
        {
            _avgSalary = new AvgSalary();
        }

        /// <summary>
        /// Test cases for average salary extraction
        /// Each test case contains: Description, Expected AvgSalary, IsSalaryCorrect flag
        /// </summary>
        public static List<AvgSalaryTestCase> GetTestCases()
        {
            return new List<AvgSalaryTestCase>
            {
                // Basic "about" patterns
                new AvgSalaryTestCase
                {
                    Title = "Software Engineer",
                    Description = "From $15 about $17 and Up to $20 per hour. Great benefits package.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "17",
                    TestDescription = "Extract 'about $17' from salary range"
                },

                new AvgSalaryTestCase
                {
                    Title = "Nurse",
                    Description = "Competitive salary about $75,000 annually with excellent benefits.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "75000",
                    TestDescription = "Extract 'about $75,000' with comma formatting"
                },

                // "Around" patterns
                new AvgSalaryTestCase
                {
                    Title = "Data Analyst",
                    Description = "Salary around $50,000 annually. Remote work available.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "50000",
                    TestDescription = "Extract 'around $50,000' pattern"
                },

                new AvgSalaryTestCase
                {
                    Title = "Marketing Manager",
                    Description = "We offer around $85K per year plus bonuses.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "85000",
                    TestDescription = "Extract 'around $85K' with K suffix"
                },

                // "Approximately" patterns
                new AvgSalaryTestCase
                {
                    Title = "Teacher",
                    Description = "Starting salary approximately $45,000 with room for growth.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "45000",
                    TestDescription = "Extract 'approximately $45,000' pattern"
                },

                new AvgSalaryTestCase
                {
                    Title = "Engineer",
                    Description = "Compensation approximately $95K annually plus benefits.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "95000",
                    TestDescription = "Extract 'approximately $95K' pattern"
                },

                // "Average" patterns
                new AvgSalaryTestCase
                {
                    Title = "Sales Representative",
                    Description = "Average salary of $60,000 with commission opportunities.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "60000",
                    TestDescription = "Extract 'average salary of $60,000' pattern"
                },

                new AvgSalaryTestCase
                {
                    Title = "Consultant",
                    Description = "Our consultants avg $120,000 per year.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "120000",
                    TestDescription = "Extract 'avg $120,000' abbreviated pattern"
                },

                // "Typically" patterns
                new AvgSalaryTestCase
                {
                    Title = "Project Manager",
                    Description = "This role typically earns $80,000 annually.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "80000",
                    TestDescription = "Extract 'typically earns $80,000' pattern"
                },

                // "Generally" patterns
                new AvgSalaryTestCase
                {
                    Title = "Developer",
                    Description = "Developers generally earn $90,000 in this position.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "90000",
                    TestDescription = "Extract 'generally earn $90,000' pattern"
                },

                // "Roughly" patterns
                new AvgSalaryTestCase
                {
                    Title = "Analyst",
                    Description = "Compensation is roughly $55,000 per year.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "55000",
                    TestDescription = "Extract 'roughly $55,000' pattern"
                },

                // Different currency symbols
                new AvgSalaryTestCase
                {
                    Title = "UK Position",
                    Description = "Salary around £45,000 per annum.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "45000",
                    TestDescription = "Extract 'around £45,000' with British pound"
                },

                new AvgSalaryTestCase
                {
                    Title = "EU Position",
                    Description = "Compensation approximately €55,000 annually.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "55000",
                    TestDescription = "Extract 'approximately €55,000' with Euro"
                },

                // No explicit average - should return null
                new AvgSalaryTestCase
                {
                    Title = "Basic Range",
                    Description = "From $15 Up to $20 per hour. No average mentioned.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = null,
                    TestDescription = "No explicit average mentioned - should return null"
                },

                new AvgSalaryTestCase
                {
                    Title = "Range Only",
                    Description = "Salary range $50,000 - $70,000 annually.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = null,
                    TestDescription = "Only range provided - should return null"
                },

                // IsSalaryCorrect = true - should not process
                new AvgSalaryTestCase
                {
                    Title = "Correct Salary",
                    Description = "Salary about $100,000 annually.",
                    IsSalaryCorrect = true,
                    ExpectedAvgSalary = null,
                    TestDescription = "IsSalaryCorrect=true - should not process"
                },

                // Edge cases with spacing variations
                new AvgSalaryTestCase
                {
                    Title = "Spacing Test",
                    Description = "Salary   around   $   65,000   annually.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "65000",
                    TestDescription = "Handle extra spacing in 'around $ 65,000'"
                },

                // Multiple average mentions - should return first valid one
                new AvgSalaryTestCase
                {
                    Title = "Multiple Averages",
                    Description = "Starting about $40,000, typically around $50,000 after training.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "40000",
                    TestDescription = "Multiple averages - should return first valid one"
                },

                // Real-world examples from test data
                new AvgSalaryTestCase
                {
                    Title = "Security Officer, Evening Shift, Security Services",
                    Description = "Pay Range: $18.31 - $24.90. Typically around $21 per hour for evening shift.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "21",
                    TestDescription = "Real-world example with 'typically around $21'"
                },

                new AvgSalaryTestCase
                {
                    Title = "Registered Nurse",
                    Description = "Pay Range: $37.17 - $55.76. Average salary approximately $46 per hour.",
                    IsSalaryCorrect = false,
                    ExpectedAvgSalary = "46",
                    TestDescription = "Real-world example with 'approximately $46'"
                }
            };
        }

        /// <summary>
        /// Runs all test cases and reports results
        /// </summary>
        public void RunAllTests()
        {
            var testCases = GetTestCases();
            int passed = 0;
            int failed = 0;

            Console.WriteLine("🧪 AVERAGE SALARY EXTRACTION TESTS");
            Console.WriteLine(new string('=', 60));

            foreach (var testCase in testCases)
            {
                try
                {
                    var job = new JobPosting
                    {
                        Title = testCase.Title,
                        Description = testCase.Description,
                        IsSalaryCorrect = testCase.IsSalaryCorrect
                    };

                    var result = _avgSalary.ExtractAverageSalary(job);
                    var success = (result == testCase.ExpectedAvgSalary) ||
                                  (result != null && testCase.ExpectedAvgSalary != null && 
                                   Math.Abs(double.Parse(result) - double.Parse(testCase.ExpectedAvgSalary)) < 0.01);

                    if (success)
                    {
                        passed++;
                        Console.WriteLine($"✅ PASS: {testCase.TestDescription}");
                    }
                    else
                    {
                        failed++;
                        Console.WriteLine($"❌ FAIL: {testCase.TestDescription}");
                        Console.WriteLine($"   Expected: {testCase.ExpectedAvgSalary ?? "null"}");
                        Console.WriteLine($"   Got: {result ?? "null"}");
                        Console.WriteLine($"   Description: {testCase.Description}");
                    }
                }
                catch (Exception ex)
                {
                    failed++;
                    Console.WriteLine($"❌ ERROR: {testCase.TestDescription}");
                    Console.WriteLine($"   Exception: {ex.Message}");
                }

                Console.WriteLine();
            }

            Console.WriteLine($"📊 TEST SUMMARY");
            Console.WriteLine($"   Total Tests: {testCases.Count}");
            Console.WriteLine($"   Passed: {passed}");
            Console.WriteLine($"   Failed: {failed}");
            Console.WriteLine($"   Success Rate: {(double)passed / testCases.Count * 100:F1}%");
        }
    }

    /// <summary>
    /// Test case data structure for average salary extraction tests
    /// </summary>
    public class AvgSalaryTestCase
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public bool IsSalaryCorrect { get; set; }
        public string ExpectedAvgSalary { get; set; }
        public string TestDescription { get; set; }
    }
}
