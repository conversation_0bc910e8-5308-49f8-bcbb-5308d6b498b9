namespace CompanyCleanTitleV2
{
    public class JobPosting
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string Responsibilities { get; set; }
        public string Skills { get; set; }
        public string Qualifications { get; set; }
        public string CompanyName { get; set; }
        public string Url { get; set; }
        public string CompanyURL { get; set; }
        public string Logo { get; set; }
        public string ApplyEmail { get; set; }
        public string BaseSalary { get; set; }
        public string MinSalary { get; set; }
        public string MaxSalary { get; set; }
        public string DatePosted { get; set; }
        public string ValidThrough { get; set; }
        public string Industry { get; set; }
        public string EmploymentType { get; set; }
        public string WorkHours { get; set; }
        public string JobBenefits { get; set; }
        public string SalaryCurrency { get; set; }
        public string PayType { get; set; }
        public string OccupationalCategory { get; set; }
        public string ExperienceRequirements { get; set; }
        public string EducationRequirements { get; set; }
        public string StreetAddress { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }
        public int SouceCode { get; set; }
        public string JobKey { get; set; }
        public bool IsFromSchema { get; set; }
        public string JobLocationType { get; set; }
        public string CompanyJobUrl { get; set; }
        public string CPC { get; set; }
        public string Country { get; set; }
        public string StateFixed { get; set; }
        public string CityFixed { get; set; }
        public string SpecialComments { get; set; }
        public int? TXNYRegionCode { get; set; }
        public string TXNYStateCode { get; set; }
        public int? TXNYMetroCode { get; set; }
        public int? TXNYCityCode { get; set; }
        public string TXNYZipCode { get; set; }
        public string TXNYCountryCode { get; set; }
        public bool IsSalaryCorrect { get; set; }
        public string ServerID { get; set; }
        public bool IsHighPriority { get; set; }
        public string AvgSalary { get; set; }
        public string ShiftType { get; set; }
    }


public class JobPosting
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string Responsibilities { get; set; }
        public string Skills { get; set; }
        public string Qualifications { get; set; }
        public string CompanyName { get; set; }
        public string Url { get; set; }
        public string CompanyURL { get; set; }
        public string Logo { get; set; }
        public string ApplyEmail { get; set; }
        public string BaseSalary { get; set; }
        public string MinSalary { get; set; }
        public string MaxSalary { get; set; }
        public string DatePosted { get; set; }
        public string ValidThrough { get; set; }
        public string Industry { get; set; }
        public string EmploymentType { get; set; }
        public string WorkHours { get; set; }
        public string JobBenefits { get; set; }
        public string SalaryCurrency { get; set; }
        public string PayType { get; set; }
        public string OccupationalCategory { get; set; }
        public string ExperienceRequirements { get; set; }
        public string EducationRequirements { get; set; }
        public string StreetAddress { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }
        public int SouceCode { get; set; }
        public string JobKey { get; set; }
        public bool IsFromSchema { get; set; }
        public string JobLocationType { get; set; }
        public string CompanyJobUrl { get; set; }
        public string CPC { get; set; }
        public string Country { get; set; }
        public string StateFixed { get; set; }
        public string CityFixed { get; set; }
        public string SpecialComments { get; set; }
        public int? TXNYRegionCode { get; set; }
        public string TXNYStateCode { get; set; }
        public int? TXNYMetroCode { get; set; }
        public int? TXNYCityCode { get; set; }
        public string TXNYZipCode { get; set; }
        public string TXNYCountryCode { get; set; }
        public bool IsSalaryCorrect { get; set; }
        public string ServerID { get; set; }
        public bool IsHighPriority { get; set; }
        public string AvgSalary { get; set; }
        public string ShiftType { get; set; }
    }

    internal class AvgSalary
    {
        public void ProcessSalaryInfo(JobPosting job, tbl_JobPosting jp)
        {
            jp.MinSalary = null; jp.MaxSalary = null; jp.SalaryDesc = null;
            if (double.TryParse(job.MinSalary, out double minSalary) && minSalary > 0)
                jp.MinSalary = minSalary;
            if (double.TryParse(job.MaxSalary, out double maxSalary) && maxSalary > 0)
                jp.MaxSalary = maxSalary;

            var payTypes = new[] { new {PayType=PayType.Yearly, Keywords=new []{"yearly","year", "annually", "annual","yr" },MinSlary=15000,MaxSalary=10000000},
        new {PayType=PayType.Monthly, Keywords=new []{ "monthly", "month" },MinSlary=1300,MaxSalary=40000 },
        new {PayType=PayType.Hourly, Keywords=new []{"hourly","hour","hr"},MinSlary=2,MaxSalary=250},
        new {PayType=PayType.Weekly, Keywords=new []{ "weekly", "week","wk" },MinSlary=310,MaxSalary=9500},
        new {PayType=PayType.Daily, Keywords=new []{ "daily", "day" },MinSlary=50,MaxSalary=1900},
        new {PayType=PayType.Biweekly, Keywords=new []{ "bi-weekly", "bi-week", "biweekly", "biweek" } ,MinSlary=620,MaxSalary=19000},
        new {PayType=PayType.BiMonthly, Keywords=new []{ "bi-monthly", "bi-month", "bimonthly", "bimonth" },MinSlary=2600,MaxSalary=80000}};
            var bonus = new List<BonusInfo>();

            var jobMinSalary = Convert.ToDouble(job.MinSalary);
            if (jobMinSalary >= _MinimalSalary || job.IsSalaryCorrect)
            {
                var jobMaxSalary = Math.Max(Convert.ToDouble(job.MaxSalary), jobMinSalary);
                jp.PayType = payTypes.FirstOrDefault(a => jobMinSalary >= a.MinSlary && jobMaxSalary <= a.MaxSalary)?.PayType.ToString() ?? "Other";
                jp.MinSalary = Math.Min(minSalary, maxSalary);
                jp.MaxSalary = Math.Max(minSalary, maxSalary);
                if (jp.MinSalary != null || jp.MaxSalary != null)
                    jp.IsEstimatedSalary = false;
                if (jp.SalaryDesc != null && jp.SalaryDesc.StartsWith("Estimated", StringComparison.OrdinalIgnoreCase))
                    jp.IsEstimatedSalary = true;
                if (jp.MinSalary != null && jp.MaxSalary != null)
                    jp.SalaryDesc = $"{jp.MinSalary} - {jp.MaxSalary}";
                else
                    jp.SalaryDesc = jp.MinSalary?.ToString();
            }
            try
            {
                var jobDesc = $"${job.MinSalary}-${job.MaxSalary} {job.PayType} / {job.Title} / {Util.GetInnerText(job.Description)}/{Util.GetInnerText(job.Responsibilities)}/{Util.GetInnerText(job.Qualifications)}";
                #region Bonus
                var bonusMatches = GetBonusMatches(_fullBonusKeywords, $"{job.Title} {job.Description}");
                if (bonusMatches.Count == 0) // use inner text
                {
                    bonusMatches = GetBonusMatches(_fullBonusKeywords, jobDesc);
                    if (bonusMatches.Count == 0)
                        bonusMatches = GetBonusMatches("bonus", jobDesc);
                }

                bonusMatches = bonusMatches.Distinct().ToList();
                foreach (var bonusItem in bonusMatches)
                {
                    if (Regex.IsMatch(bonusItem.Value, @"\b(401K|yearly|annual|hourly|year|hour)\b", RegexOptions.IgnoreCase))
                        continue;
                    var salaryValues = Util.SalSplit(bonusItem.Value, "-", "/", "to", "and", "or").Select(a => Convert.ToDouble(Util.GetCleanSalary(a))).Where(a => a > 0).OrderBy(a => a).ToArray();
                    if (!salaryValues.Any())
                        continue;
                    if (salaryValues.Length == 2 && salaryValues.Max() / salaryValues.Min() > 100)
                    {
                        // fix case $75-80k and $41,299 - $45-000;
                        salaryValues = salaryValues.Select(a => a < 1000 ? a * 1000 : a).OrderBy(a => a).ToArray();
                    }
                    var bonusMinSalary = salaryValues.First();
                    var bonusMaxSalary = salaryValues.Last();
                    bonus.Add(new BonusInfo { BounsMin = bonusMinSalary, BounsMax = bonusMaxSalary, BonusType = 1 });
                }
                #endregion
                if (!job.IsSalaryCorrect)
                {
                    var salaryMatches = Regex.Matches(jobDesc, @"([\$£₹€]?\s*[\d,.]+K?\s*(-|–|/|to|and|or)+\s*(maximum)*\s*[\$£₹€]?\s*[\d,.]+K?)|( [\d,.]+K?\s*(-|–|/|to|and|or)+\s*[\$£₹€]?\s*[\d,.]+K?)", RegexOptions.IgnoreCase).Cast<Match>().ToList();
                    salaryMatches.AddRange(Regex.Matches(jobDesc, @"[\$£₹€]\s*[\d,.]+K?", RegexOptions.IgnoreCase).Cast<Match>().Where(a => salaryMatches.Count(b => b.Value.Contains(a.Value)) == 0));
                    var results = new List<SalaryInfo>();
                    var salaryStopKeywords = $@"\b(revenue|billion|million|bonus|bonuses|allowance|reward|commission|incentive|tuition|reimbursement|benefit|stock|bonus|{_fullBonusKeywords})[es]*\b";
                    var salaryKeywordsPattern = @"\b(salary|salaried|pay)s?\b";
                    foreach (var salaryItem in salaryMatches)
                    {
                        if (!Regex.IsMatch(salaryItem.Value, @"[\$£₹€]"))
                            continue;
                        PayType matchPayType = PayType.Other;
                        var salaryValues = Util.SalSplit(salaryItem.Value, "-", "/", "to", "and", "or", "–").Select(a => Convert.ToDouble(Util.GetCleanSalary(a))).Where(a => a > 0).OrderBy(a => a).ToArray();
                        if (!salaryValues.Any())
                            continue;
                        if (salaryValues.Length == 2 && salaryValues.Max() / salaryValues.Min() > 100)
                        {
                            // fix case $75-80k and $41,299 - $45-000;
                            salaryValues = salaryValues.Select(a => a < 1000 ? a * 1000 : a).OrderBy(a => a).ToArray();
                        }
                        minSalary = salaryValues.First();
                        maxSalary = salaryValues.Last();
                        if (minSalary < _MinimalSalary)
                            continue;
                        var salaryPattern = Regex.Escape(salaryItem.Value);
                        var pattern = $@"\b({salaryStopKeywords})\b.{{0,20}}({salaryPattern})|({salaryPattern}).{{0,20}}\b({salaryStopKeywords})\b|{salaryPattern}[bmn]+\b";
                        if (Regex.IsMatch(jobDesc, pattern, RegexOptions.IgnoreCase))
                        {
                            var allPayTypeKeywords = string.Join("|", payTypes.SelectMany(a => a.Keywords)) + "|" + salaryKeywordsPattern;
                            pattern = $@"\b({allPayTypeKeywords})s?\b.{{0,20}}({salaryPattern})|({salaryPattern}).{{0,20}}\b({allPayTypeKeywords})s?\b";
                            if (!Regex.IsMatch(jobDesc, pattern, RegexOptions.IgnoreCase))
                                continue;
                        }
                        ConfidenceType? confidence = null;
                        foreach (var payType in payTypes)
                        {
                            pattern = $@"\b({string.Join("|", payType.Keywords)})s?\b[: \w\d]{{0,20}}({salaryPattern})|({salaryPattern})[: \w\d/]{{0,20}}\b({string.Join("|", payType.Keywords)})s?\b";
                            var payTypeMatched = Regex.IsMatch(jobDesc, pattern, RegexOptions.IgnoreCase);
                            var salaryRangeMatched = salaryValues.Count(a => a >= payType.MinSlary && a <= payType.MaxSalary) == salaryValues.Length;
                            if (payTypeMatched)
                            {
                                confidence = ConfidenceType.High;
                                matchPayType = payType.PayType;
                                break;
                            }
                        }
                        if (confidence == null)
                        {
                            foreach (var payType in payTypes)
                            {
                                pattern = $@"\b({string.Join("|", payType.Keywords)})s?\b[: \w\d]{{0,20}}({salaryPattern})|({salaryPattern})[: \w\d/]{{0,20}}\b({string.Join("|", payType.Keywords)})s?\b";
                                var payTypeMatched = Regex.IsMatch(jobDesc, pattern, RegexOptions.IgnoreCase);
                                var salaryRangeMatched = salaryValues.Count(a => a >= payType.MinSlary && a <= payType.MaxSalary) == salaryValues.Length;
                                if (!salaryRangeMatched)
                                    continue;
                                matchPayType = payType.PayType;
                                if (matchPayType == PayType.Yearly)
                                    confidence = ConfidenceType.Median;
                                else if (matchPayType == PayType.Hourly)
                                    confidence = ConfidenceType.Low;

                                pattern = $@"{salaryKeywordsPattern}.{{0,20}}({salaryPattern})|({salaryPattern}).{{0,20}}{salaryKeywordsPattern}";
                                if (confidence != ConfidenceType.High && Regex.IsMatch(jobDesc, pattern, RegexOptions.IgnoreCase))
                                    confidence = ConfidenceType.High;
                                break;
                            }
                        }

                        if (confidence.HasValue && !results.Any(a => a.MinSalary == minSalary && a.MaxSalary == maxSalary && a.ConfidenceType == confidence.Value))
                            results.Add(new SalaryInfo { MinSalary = minSalary, MaxSalary = maxSalary, ConfidenceType = confidence.Value, PayType = matchPayType });
                    }
                    results = results.Where(a => a.MinSalary >= _MinimalSalary && a.MaxSalary < 10000000).OrderByDescending(a => a.ConfidenceType).ThenBy(a => a.PayType).ThenBy(a => a.MinSalary == a.MaxSalary).ThenBy(a => a.MinSalary).ToList();
                    var resultGroup = results.GroupBy(a => new { a.ConfidenceType, a.PayType }).FirstOrDefault();
                    var result = resultGroup?.FirstOrDefault();
                    if (result != null)
                    {
                        jp.MinSalary = result.MinSalary;
                        jp.MaxSalary = result.MaxSalary;
                        if (jp.MinSalary == jp.MaxSalary)
                            jp.MaxSalary = resultGroup?.LastOrDefault()?.MaxSalary;
                        jp.PayType = result.PayType.ToString();
                        jp.IsEstimatedSalary = false;
                        jp.SalaryDesc = $"{jp.MinSalary} - {jp.MaxSalary}";
                    }
                    bonus = bonus.Where(a => a.BounsMin != result?.MinSalary && a.BounsMin != result?.MaxSalary && a.BounsMax != result?.MinSalary && a.BounsMax != result?.MaxSalary).ToList();
                }
                else
                {
                    var minsalary = Convert.ToDouble(job.MinSalary);
                    var maxsalary = Convert.ToDouble(job.MaxSalary);
                    bonus = bonus.Where(a => a.BounsMin != minsalary && a.BounsMin != maxsalary && a.BounsMax != minsalary && a.BounsMax != maxsalary).ToList();
                }

                if (bonus.Count > 0)
                {
                    var bonusTypeGroup = bonus.GroupBy(a => a.BonusType);
                    foreach (var bonusItem in bonusTypeGroup)
                    {
                        var bestItem = bonusItem.First();
                        bestItem.BounsMax = bonusItem.Where(a => a.BounsMax >= bestItem.BounsMax).Max(a => a.BounsMax);
                    }
                    jp.Bonus = JsonConvert.SerializeObject(bonus);
                }

                string[] timeUnitKey = new string[]
                {
            "yearly", "year", "annually", "annual", "yr" ,
            "monthly" , "month",
            "hourly","hour","hr",
            "weekly","week","wk",
            "daily","day",
            "bi-weekly","bi-week","biweekly","biweek",
            "bi-monthly","bi-month","bimonthly","bimonth"
                };
                string[] maxSalaryPhrase = new string[]
                {
            "up to", "max", "maximum", "no more than", "upwards of", "as high as", "as much as", "earn up to"
                };
                string maxSalaryPattern = $@"(?:{string.Join("|", maxSalaryPhrase)})\s*\$\s*([\d,]+(?:\.\d{1,2})?)\s*(?:[^\w\s]*?)\s*({string.Join("|", timeUnitKey)})";
                MatchCollection matches = Regex.Matches(Util.GetInnerText(job.Description), maxSalaryPattern, RegexOptions.IgnoreCase);
                if (matches.Count > 0)
                {
                    if (jp.MinSalary == jp.MaxSalary && jp.MaxSalary != 0 && jp.MaxSalary != null)
                    {
                        jp.MinSalary = 0;
                    }
                }
            }
            catch (Exception e)
            {
                log.Error(e);
            }
        }

    }
}