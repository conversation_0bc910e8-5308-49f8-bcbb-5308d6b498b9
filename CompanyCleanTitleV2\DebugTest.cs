using System;
using System.Text.RegularExpressions;
using CompanyCleanTitleV2;

namespace CompanyCleanTitleV2.Tests
{
    public class DebugTest
    {
        public static void TestSalaryExtraction()
        {
            var avgSalary = new AvgSalary();
            
            // Test the failing case
            var job = new JobPosting
            {
                Title = "Software Engineer",
                Description = "From $15 about $17 and Up to $20 per hour. Great benefits package.",
                IsSalaryCorrect = false
            };

            Console.WriteLine("Testing failing case:");
            Console.WriteLine($"Description: {job.Description}");
            
            var result = avgSalary.ExtractAverageSalary(job);
            Console.WriteLine($"Result: '{result ?? "null"}'");
            
            // Test the regex pattern directly
            var avgKeywords = new[] { "about", "around", "approximately", "avg", "average", "typically", "generally", "roughly" };
            var avgKeywordPattern = string.Join("|", avgKeywords);
            var avgSalaryPattern = $@"\b({avgKeywordPattern})\s*[\$£₹€]\s*([\d,.]+K?)\b";
            
            Console.WriteLine($"\nRegex pattern: {avgSalaryPattern}");
            
            var matches = Regex.Matches(job.Description, avgSalaryPattern, RegexOptions.IgnoreCase);
            Console.WriteLine($"Number of matches: {matches.Count}");
            
            foreach (Match match in matches)
            {
                Console.WriteLine($"Match: '{match.Value}'");
                for (int i = 0; i < match.Groups.Count; i++)
                {
                    Console.WriteLine($"  Group {i}: '{match.Groups[i].Value}'");
                }
            }
            
            // Test with a simpler pattern
            var simplePattern = @"about\s*\$\s*(\d+)";
            var simpleMatches = Regex.Matches(job.Description, simplePattern, RegexOptions.IgnoreCase);
            Console.WriteLine($"\nSimple pattern matches: {simpleMatches.Count}");
            
            foreach (Match match in simpleMatches)
            {
                Console.WriteLine($"Simple match: '{match.Value}'");
                for (int i = 0; i < match.Groups.Count; i++)
                {
                    Console.WriteLine($"  Group {i}: '{match.Groups[i].Value}'");
                }
            }
        }
    }
}
