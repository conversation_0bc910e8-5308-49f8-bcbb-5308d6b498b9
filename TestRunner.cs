using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using CompanyCleanTitleV2;

namespace CompanyCleanTitleV2.Tests
{
    /// <summary>
    /// Test runner for average salary extraction functionality
    /// Tests against real job data and synthetic test cases
    /// </summary>
    public class TestRunner
    {
        private readonly AvgSalary _avgSalary;
        private readonly string _testDataPath;

        public TestRunner()
        {
            _avgSalary = new AvgSalary();
            _testDataPath = @"data\Job_20250408074026412_289.json";
        }

        /// <summary>
        /// Main test execution method
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("🚀 AVERAGE SALARY EXTRACTION TEST SUITE");
            Console.WriteLine(new string('=', 70));
            Console.WriteLine();

            // Run synthetic test cases
            RunSyntheticTests();
            Console.WriteLine();

            // Run tests against real job data
            RunRealDataTests();
            Console.WriteLine();

            // Run regex pattern validation tests
            RunRegexPatternTests();
            Console.WriteLine();

            // Run edge case tests
            RunEdgeCaseTests();
        }

        /// <summary>
        /// Run synthetic test cases with known expected results
        /// </summary>
        private void RunSyntheticTests()
        {
            Console.WriteLine("📝 SYNTHETIC TEST CASES");
            Console.WriteLine(new string('-', 40));

            var tests = new AvgSalaryTests();
            tests.RunAllTests();
        }

        /// <summary>
        /// Test against real job posting data
        /// </summary>
        private void RunRealDataTests()
        {
            Console.WriteLine("📊 REAL JOB DATA TESTS");
            Console.WriteLine(new string('-', 40));

            try
            {
                if (!File.Exists(_testDataPath))
                {
                    Console.WriteLine($"❌ Test data file not found: {_testDataPath}");
                    return;
                }

                var jsonContent = File.ReadAllText(_testDataPath);
                var jobs = JsonConvert.DeserializeObject<List<JobPosting>>(jsonContent);

                if (jobs == null || !jobs.Any())
                {
                    Console.WriteLine("❌ No job data found in test file");
                    return;
                }

                Console.WriteLine($"📋 Processing {jobs.Count} real job postings...");
                Console.WriteLine();

                int processedCount = 0;
                int avgSalaryFoundCount = 0;

                foreach (var job in jobs)
                {
                    if (!job.IsSalaryCorrect)
                    {
                        processedCount++;
                        var avgSalary = _avgSalary.ExtractAverageSalary(job);
                        
                        if (!string.IsNullOrEmpty(avgSalary))
                        {
                            avgSalaryFoundCount++;
                            Console.WriteLine($"✅ Found average salary in: {job.Title}");
                            Console.WriteLine($"   Average Salary: ${avgSalary}");
                            Console.WriteLine($"   Description snippet: {GetDescriptionSnippet(job.Description, 100)}");
                            Console.WriteLine();
                        }
                    }
                }

                Console.WriteLine($"📈 REAL DATA SUMMARY:");
                Console.WriteLine($"   Jobs with IsSalaryCorrect=false: {processedCount}");
                Console.WriteLine($"   Average salaries found: {avgSalaryFoundCount}");
                Console.WriteLine($"   Detection rate: {(double)avgSalaryFoundCount / processedCount * 100:F1}%");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error processing real data: {ex.Message}");
            }
        }

        /// <summary>
        /// Test regex patterns for various average salary expressions
        /// </summary>
        private void RunRegexPatternTests()
        {
            Console.WriteLine("🔍 REGEX PATTERN VALIDATION TESTS");
            Console.WriteLine(new string('-', 40));

            var patternTests = new List<(string description, string text, bool shouldMatch)>
            {
                ("Basic 'about' pattern", "Salary about $50,000", true),
                ("'Around' with spacing", "around   $   75K", true),
                ("'Approximately' formal", "approximately $100,000 annually", true),
                ("'Average salary of' pattern", "average salary of $65,000", true),
                ("'Typically earns' pattern", "typically earns $80,000", true),
                ("'Generally pays' pattern", "generally pays $90,000", true),
                ("'Roughly' informal", "roughly $55K per year", true),
                ("No currency symbol", "about 50000", false),
                ("Wrong context", "about 5 years experience", false),
                ("Range without average", "$50,000 - $70,000", false),
                ("Different currency", "around £45,000", true),
                ("Euro currency", "approximately €60,000", true),
                ("Multiple spaces", "about    $    85,000", true),
                ("K suffix", "around $75K", true),
                ("Decimal amounts", "about $52.5K", true)
            };

            int passed = 0;
            int total = patternTests.Count;

            foreach (var test in patternTests)
            {
                var job = new JobPosting
                {
                    Title = "Test Job",
                    Description = test.text,
                    IsSalaryCorrect = false
                };

                var result = _avgSalary.ExtractAverageSalary(job);
                bool foundMatch = !string.IsNullOrEmpty(result);

                if (foundMatch == test.shouldMatch)
                {
                    passed++;
                    Console.WriteLine($"✅ {test.description}: '{test.text}' -> {(foundMatch ? $"${result}" : "No match")}");
                }
                else
                {
                    Console.WriteLine($"❌ {test.description}: '{test.text}' -> Expected {(test.shouldMatch ? "match" : "no match")}, got {(foundMatch ? $"${result}" : "no match")}");
                }
            }

            Console.WriteLine();
            Console.WriteLine($"📊 REGEX PATTERN SUMMARY:");
            Console.WriteLine($"   Total tests: {total}");
            Console.WriteLine($"   Passed: {passed}");
            Console.WriteLine($"   Success rate: {(double)passed / total * 100:F1}%");
        }

        /// <summary>
        /// Test edge cases and error conditions
        /// </summary>
        private void RunEdgeCaseTests()
        {
            Console.WriteLine("⚠️  EDGE CASE TESTS");
            Console.WriteLine(new string('-', 40));

            var edgeCases = new List<(string description, JobPosting job, string expectedResult)>
            {
                ("Null description", new JobPosting { Title = "Test", Description = null, IsSalaryCorrect = false }, null),
                ("Empty description", new JobPosting { Title = "Test", Description = "", IsSalaryCorrect = false }, null),
                ("IsSalaryCorrect = true", new JobPosting { Title = "Test", Description = "about $50,000", IsSalaryCorrect = true }, null),
                ("Very small salary", new JobPosting { Title = "Test", Description = "about $500", IsSalaryCorrect = false }, null),
                ("Very large salary", new JobPosting { Title = "Test", Description = "about $5,000,000", IsSalaryCorrect = false }, "5000000"),
                ("Multiple currencies", new JobPosting { Title = "Test", Description = "about $50,000 or £40,000", IsSalaryCorrect = false }, "50000"),
                ("HTML in description", new JobPosting { Title = "Test", Description = "<p>Salary about <strong>$60,000</strong> annually</p>", IsSalaryCorrect = false }, "60000"),
                ("Special characters", new JobPosting { Title = "Test", Description = "Salary around $75,000.00 per year!", IsSalaryCorrect = false }, "75000")
            };

            int passed = 0;
            int total = edgeCases.Count;

            foreach (var test in edgeCases)
            {
                try
                {
                    var result = _avgSalary.ExtractAverageSalary(test.job);
                    
                    if (result == test.expectedResult)
                    {
                        passed++;
                        Console.WriteLine($"✅ {test.description}: Expected '{test.expectedResult}', got '{result}'");
                    }
                    else
                    {
                        Console.WriteLine($"❌ {test.description}: Expected '{test.expectedResult}', got '{result}'");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ {test.description}: Exception - {ex.Message}");
                }
            }

            Console.WriteLine();
            Console.WriteLine($"📊 EDGE CASE SUMMARY:");
            Console.WriteLine($"   Total tests: {total}");
            Console.WriteLine($"   Passed: {passed}");
            Console.WriteLine($"   Success rate: {(double)passed / total * 100:F1}%");
        }

        /// <summary>
        /// Helper method to get a snippet of description text
        /// </summary>
        private string GetDescriptionSnippet(string description, int maxLength)
        {
            if (string.IsNullOrEmpty(description))
                return "";

            var cleanText = Util.GetInnerText(description);
            return cleanText.Length <= maxLength ? cleanText : cleanText.Substring(0, maxLength) + "...";
        }
    }

    /// <summary>
    /// Program entry point for running tests
    /// </summary>
    public class Program
    {
        public static void Main(string[] args)
        {
            var testRunner = new TestRunner();
            testRunner.RunAllTests();
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
